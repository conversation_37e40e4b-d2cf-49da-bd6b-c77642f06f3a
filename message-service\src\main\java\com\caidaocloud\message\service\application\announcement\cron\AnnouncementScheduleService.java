package com.caidaocloud.message.service.application.announcement.cron;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.message.service.application.announcement.service.AnnouncementStatusService;
import com.caidaocloud.message.service.domain.base.util.UserContext;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 公告定时任务服务
 *
 * <AUTHOR>
 * @date 2025/7/11
 */
@Slf4j
@Service
public class AnnouncementScheduleService {

    @Resource
    private AnnouncementStatusService announcementStatusService;

    @Value("${caidaocloud.tenant:}")
    private String tenantListStr;

    /**
     * 公告生效和失效定时任务
     * 处理已发布且生效的公告，将其复制到已生效公告表
     * 处理已发布且失效的公告，修改状态为失效并删除生效数据
     */
    @XxlJob("announcementStatusJobHandler")
    public ReturnT<String> announcementStatusJobHandler() {
        XxlJobHelper.log("XxlJob announcementStatusJobHandler start");
        log.info("cronTask[Announcement Status]------------------------start execution,time {}",
                System.currentTimeMillis());

        List<String> tenantList = getTenantList();
        if (tenantList.isEmpty()) {
            log.info("Announcement Status: tenantList is empty, execution end");
            XxlJobHelper.log("tenantList is empty, execution end");
            return ReturnT.SUCCESS;
        }

        int totalEffectiveProcessed = 0;
        int totalExpiredProcessed = 0;

        // 遍历租户，处理公告状态
        for (String tenantId : tenantList) {
            try {
                // 设置租户上下文
                setTenantContext(tenantId);

                // 处理已发布且生效的公告
                int effectiveProcessed = announcementStatusService.processPublishedAndEffectiveAnnouncements();
                totalEffectiveProcessed += effectiveProcessed;
                log.info("租户[{}]处理生效公告数量：{}", tenantId, effectiveProcessed);

                // 处理已发布且失效的公告
                int expiredProcessed = announcementStatusService.processPublishedAndExpiredAnnouncements();
                totalExpiredProcessed += expiredProcessed;
                log.info("租户[{}]处理失效公告数量：{}", tenantId, expiredProcessed);

            } catch (Exception e) {
                log.error("处理租户[{}]公告状态失败", tenantId, e);
                XxlJobHelper.log("处理租户[{}]公告状态失败：{}", tenantId, e.getMessage());
            } finally {
                // 清理上下文
                clearTenantContext();
            }
        }

        log.info("cronTask[Announcement Status]------------------------end execution,time {}, 总计处理生效公告：{}，失效公告：{}",
                System.currentTimeMillis(), totalEffectiveProcessed, totalExpiredProcessed);
        XxlJobHelper.log("XxlJob announcementStatusJobHandler end, 总计处理生效公告：{}，失效公告：{}",
                totalEffectiveProcessed, totalExpiredProcessed);

        return ReturnT.SUCCESS;
    }
    //
    // /**
    //  * 公告生效定时任务
    //  * 专门处理已发布且生效的公告
    //  */
    // @XxlJob("announcementEffectiveJobHandler")
    // public ReturnT<String> announcementEffectiveJobHandler() {
    //     XxlJobHelper.log("XxlJob announcementEffectiveJobHandler start");
    //     log.info("cronTask[Announcement Effective]------------------------start execution,time {}",
    //             System.currentTimeMillis());
    //
    //     List<String> tenantList = getTenantList();
    //     if (tenantList.isEmpty()) {
    //         log.info("Announcement Effective: tenantList is empty, execution end");
    //         XxlJobHelper.log("tenantList is empty, execution end");
    //         return ReturnT.SUCCESS;
    //     }
    //
    //     int totalProcessed = 0;
    //
    //     // 遍历租户，处理公告生效
    //     for (String tenantId : tenantList) {
    //         try {
    //             // 设置租户上下文
    //             setTenantContext(tenantId);
    //
    //             // 处理已发布且生效的公告
    //             int processed = announcementStatusService.processPublishedAndEffectiveAnnouncements();
    //             totalProcessed += processed;
    //             log.info("租户[{}]处理生效公告数量：{}", tenantId, processed);
    //
    //         } catch (Exception e) {
    //             log.error("处理租户[{}]公告生效失败", tenantId, e);
    //             XxlJobHelper.log("处理租户[{}]公告生效失败：{}", tenantId, e.getMessage());
    //         } finally {
    //             // 清理上下文
    //             clearTenantContext();
    //         }
    //     }
    //
    //     log.info("cronTask[Announcement Effective]------------------------end execution,time {}, 总计处理：{}",
    //             System.currentTimeMillis(), totalProcessed);
    //     XxlJobHelper.log("XxlJob announcementEffectiveJobHandler end, 总计处理：{}", totalProcessed);
    //
    //     return ReturnT.SUCCESS;
    // }
    //
    // /**
    //  * 公告失效定时任务
    //  * 专门处理已发布且失效的公告
    //  */
    // @XxlJob("announcementExpiredJobHandler")
    // public ReturnT<String> announcementExpiredJobHandler() {
    //     XxlJobHelper.log("XxlJob announcementExpiredJobHandler start");
    //     log.info("cronTask[Announcement Expired]------------------------start execution,time {}",
    //             System.currentTimeMillis());
    //
    //     List<String> tenantList = getTenantList();
    //     if (tenantList.isEmpty()) {
    //         log.info("Announcement Expired: tenantList is empty, execution end");
    //         XxlJobHelper.log("tenantList is empty, execution end");
    //         return ReturnT.SUCCESS;
    //     }
    //
    //     int totalProcessed = 0;
    //
    //     // 遍历租户，处理公告失效
    //     for (String tenantId : tenantList) {
    //         try {
    //             // 设置租户上下文
    //             setTenantContext(tenantId);
    //
    //             // 处理已发布且失效的公告
    //             int processed = announcementStatusService.processPublishedAndExpiredAnnouncements();
    //             totalProcessed += processed;
    //             log.info("租户[{}]处理失效公告数量：{}", tenantId, processed);
    //
    //         } catch (Exception e) {
    //             log.error("处理租户[{}]公告失效失败", tenantId, e);
    //             XxlJobHelper.log("处理租户[{}]公告失效失败：{}", tenantId, e.getMessage());
    //         } finally {
    //             // 清理上下文
    //             clearTenantContext();
    //         }
    //     }
    //
    //     log.info("cronTask[Announcement Expired]------------------------end execution,time {}, 总计处理：{}",
    //             System.currentTimeMillis(), totalProcessed);
    //     XxlJobHelper.log("XxlJob announcementExpiredJobHandler end, 总计处理：{}", totalProcessed);
    //
    //     return ReturnT.SUCCESS;
    // }

    /**
     * 获取租户列表
     */
    private List<String> getTenantList() {
        if (tenantListStr == null || tenantListStr.trim().isEmpty()) {
            return Arrays.asList();
        }
        return Arrays.asList(tenantListStr.split(","));
    }

    /**
     * 设置租户上下文
     */
    private void setTenantContext(String tenantId) {
        // 设置安全用户信息
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId(tenantId);
        userInfo.setUserId(0L); // 系统跑批用户ID默认为0
        SecurityUserUtil.setSecurityUserInfo(userInfo);

        // 设置用户上下文
        UserInfo user = new UserInfo();
        user.setTenantId(tenantId);
        user.doSetUserId(0L); // 系统跑批用户ID默认为0
        UserContext.setCurrentUser(user);

        log.debug("设置租户上下文完成，tenantId：{}", tenantId);
    }

    /**
     * 清理租户上下文
     */
    private void clearTenantContext() {
        SecurityUserUtil.removeSecurityUserInfo();
        UserContext.remove();
        log.debug("清理租户上下文完成");
    }
}
