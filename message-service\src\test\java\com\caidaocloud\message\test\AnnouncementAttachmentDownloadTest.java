package com.caidaocloud.message.test;

import com.caidaocloud.config.TestExceptionHandler;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.message.service.MessageApplication;
import com.caidaocloud.message.service.application.announcement.dto.AnnouncementDto;
import com.caidaocloud.message.service.application.announcement.service.AnnouncementApplicationService;
import com.caidaocloud.message.service.application.announcement.service.AnnouncementStatusService;
import com.caidaocloud.message.service.application.feign.MasterdataFeignClientV2;
import com.caidaocloud.message.service.domain.announcement.entity.Announcement;
import com.caidaocloud.message.service.domain.announcement.entity.AnnouncementContent;
import com.caidaocloud.message.service.domain.announcement.enums.ReceiveType;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementContentRepository;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementRepository;
import com.caidaocloud.message.service.interfaces.dto.base.UserDetailVo;
import com.caidaocloud.message.service.interfaces.facade.AnnouncementController;
import com.caidaocloud.oss.service.OssService;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.WebApplicationContext;

import javax.annotation.Resource;
import java.util.Arrays;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 公告附件下载接口测试类
 *
 * <AUTHOR> Zhou
 * @date 2025/7/25
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = MessageApplication.class)
@Import(TestExceptionHandler.class) // 引入测试专用的异常处理器
public class AnnouncementAttachmentDownloadTest {


    @Resource
    private WebApplicationContext webApplicationContext;

    @Resource
    private AnnouncementApplicationService announcementApplicationService;

    @Resource
    private AnnouncementStatusService announcementStatusService;

    @Resource
    private AnnouncementRepository announcementRepository;

    @Resource
    private AnnouncementContentRepository announcementContentRepository;

    @MockBean
    private MasterdataFeignClientV2 masterdataFeignClientV2;

    @MockBean
    private OssService ossService;

    @Autowired
    private AnnouncementController announcementController;

    private MockMvc mockMvc;

    @Before
    public void setUp() {
        // 初始化MockMvc
        mockMvc = MockMvcBuilders.standaloneSetup(announcementController).build();

        // 设置测试用户信息
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId("11");
        userInfo.setUserId(1923451746924549L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);

        // 设置MasterdataFeignClientV2的mock返回数据
        setupMasterdataFeignClientMock();

        // 设置OSS服务的mock返回数据
        setupOssServiceMock();
    }

    /**
     * 设置MasterdataFeignClientV2的mock数据
     */
    private void setupMasterdataFeignClientMock() {
        // 创建mock用户详情数据 - 使用全参数构造方法
        UserDetailVo mockUserDetail = new UserDetailVo(
                "http://test-pc-logo.png", // pcTenantLogo
                "http://test-app-logo.png", // appTenantLogo
                "测试公司", // companyName
                "TEST_COMPANY", // companyCode
                "hr,message", // licencedModules
                "2047437853497344", // empId
                "测试用户", // name
                "2143720577071379", // orgId
                "T001", // workno
                "TestUser", // enName
                "测试岗位", // postName
                "http://test-avatar.png" // headPortrait
        );

        // mock loadCurrentUserDetail方法
        Result<UserDetailVo> mockResult = Result.ok(mockUserDetail);

        Mockito.when(masterdataFeignClientV2.loadCurrentUserDetail()).thenReturn(mockResult);
    }

    /**
     * 设置OSS服务的mock数据
     */
    private void setupOssServiceMock() {
        try {
            // mock OSS下载方法，返回测试文件流
            java.io.ByteArrayInputStream mockInputStream = new java.io.ByteArrayInputStream(
                    "这是测试附件内容".getBytes("UTF-8"));
            Mockito.when(ossService.getInputStream(Mockito.anyString())).thenReturn(mockInputStream);
        } catch (Exception e) {
            log.error("设置OSS服务mock失败", e);
        }
    }

    /**
     * 测试成功下载公告附件
     */
    @Test
    public void testDownloadAnnouncementAttachmentSuccess() throws Exception {
        log.info("开始测试成功下载公告附件");

        // 1. 创建并发布测试公告
        Announcement announcement = createAndPublishTestAnnouncement("带附件的测试公告");
        String announcementId = announcement.getBid();

        // 2. 获取附件ID
        AnnouncementContent content = announcementContentRepository.findByAnnouncementId(announcementId);
        String attachmentId = content.getAttachmentList().get(0).getBid();

        // 3. 执行下载请求
        mockMvc.perform(get("/api/msg/announcement/v1/published/detail/attachment")
                .param("announcementId", announcementId)
                .param("attachmentId", attachmentId)
                .header("domain", "test.domain.com"))
                .andExpect(status().isOk());

        log.info("成功下载公告附件测试通过");
    }

    /**
     * 测试下载不存在的公告附件
     */
    @Test
    public void testDownloadNonExistentAnnouncement() throws Exception {
        log.info("开始测试下载不存在的公告附件");

        // 执行下载请求
        mockMvc.perform(get("/api/msg/announcement/v1/published/detail/attachment")
                .param("announcementId", "non-existent-id")
                .param("attachmentId", "attachment-id")
                .header("domain", "test.domain.com"))
                .andExpect(status().is5xxServerError());

        log.info("下载不存在的公告附件测试通过");
    }

    /**
     * 测试下载不存在的附件
     */
    @Test
    public void testDownloadNonExistentAttachment() throws Exception {
        log.info("开始测试下载不存在的附件");

        // 1. 创建并发布测试公告
        Announcement announcement = createAndPublishTestAnnouncement("测试公告");
        String announcementId = announcement.getBid();

        // 2. 执行下载请求（使用不存在的附件ID）
        mockMvc.perform(get("/api/msg/announcement/v1/published/detail/attachment")
                .param("announcementId", announcementId)
                .param("attachmentId", "non-existent-attachment-id")
                .header("domain", "test.domain.com"))
                .andExpect(status().is5xxServerError());

        log.info("下载不存在的附件测试通过");
    }

    /**
     * 测试无效域名下载附件
     */
    @Test
    public void testDownloadWithInvalidDomain() throws Exception {
        log.info("开始测试无效域名下载附件");

        // 1. 创建并发布测试公告
        Announcement announcement = createAndPublishTestAnnouncement("测试公告");
        String announcementId = announcement.getBid();

        // 2. 获取附件ID
        AnnouncementContent content = announcementContentRepository.findByAnnouncementId(announcementId);
        String attachmentId = content.getAttachmentList().get(0).getBid();

        // 3. 执行下载请求（使用无效域名）
        mockMvc.perform(get("/api/msg/announcement/v1/published/detail/attachment")
                .param("announcementId", announcementId)
                .param("attachmentId", attachmentId)
                .header("domain", "invalid.domain.com"))
                .andExpect(status().is5xxServerError());

        log.info("无效域名下载附件测试通过");
    }

    /**
     * 测试缺少必需参数
     */
    @Test
    public void testDownloadWithMissingParameters() throws Exception {
        log.info("开始测试缺少必需参数");

        // 1. 缺少公告ID
        mockMvc.perform(get("/api/msg/announcement/v1/published/detail/attachment")
                .param("attachmentId", "attachment-id"))
                .andExpect(status().isBadRequest());

        // 2. 缺少附件ID
        mockMvc.perform(get("/api/msg/announcement/v1/published/detail/attachment")
                .param("announcementId", "announcement-id"))
                .andExpect(status().isBadRequest());

        log.info("缺少必需参数测试通过");
    }

    /**
     * 测试空参数值
     */
    @Test
    public void testDownloadWithEmptyParameters() throws Exception {
        log.info("开始测试空参数值");

        // 1. 空公告ID
        mockMvc.perform(get("/api/msg/announcement/v1/published/detail/attachment")
                .param("announcementId", "")
                .param("attachmentId", "attachment-id"))
                .andExpect(status().isBadRequest());

        // 2. 空附件ID
        mockMvc.perform(get("/api/msg/announcement/v1/published/detail/attachment")
                .param("announcementId", "announcement-id")
                .param("attachmentId", ""))
                .andExpect(status().isBadRequest());

        log.info("空参数值测试通过");
    }

    /**
     * 测试未发布公告的附件下载
     */
    @Test
    public void testDownloadUnpublishedAnnouncementAttachment() throws Exception {
        log.info("开始测试未发布公告的附件下载");

        // 1. 创建但不发布测试公告
        AnnouncementDto dto = createTestAnnouncementDto("未发布的测试公告");
        String announcementId = announcementApplicationService.createAnnouncement(dto);
        // 注意：这里不调用发布方法

        // 2. 获取附件ID
        AnnouncementContent content = announcementContentRepository.findByAnnouncementId(announcementId);
        String attachmentId = content.getAttachmentList().get(0).getBid();

        // 3. 执行下载请求
        mockMvc.perform(get("/api/msg/announcement/v1/published/detail/attachment")
                .param("announcementId", announcementId)
                .param("attachmentId", attachmentId)
                .header("domain", "test.domain.com"))
                .andExpect(status().is5xxServerError());

        log.info("未发布公告的附件下载测试通过");
    }

    /**
     * 测试不同域名头的处理
     */
    @Test
    public void testDownloadWithDifferentDomainHeaders() throws Exception {
        log.info("开始测试不同域名头的处理");

        // 1. 创建并发布测试公告
        Announcement announcement = createAndPublishTestAnnouncement("域名测试公告");
        String announcementId = announcement.getBid();

        // 2. 获取附件ID
        AnnouncementContent content = announcementContentRepository.findByAnnouncementId(announcementId);
        String attachmentId = content.getAttachmentList().get(0).getBid();

        // 3. 测试有效域名
        mockMvc.perform(get("/api/msg/announcement/v1/published/detail/attachment")
                .param("announcementId", announcementId)
                .param("attachmentId", attachmentId)
                .header("domain", "test.domain.com"))
                .andExpect(status().isOk());

        // 4. 测试无域名头（应该允许，因为没有域名限制时允许所有域名）
        mockMvc.perform(get("/api/msg/announcement/v1/published/detail/attachment")
                .param("announcementId", announcementId)
                .param("attachmentId", attachmentId))
                .andExpect(status().is5xxServerError());

        log.info("不同域名头的处理测试通过");
    }

    /**
     * 测试完整的附件下载流程
     * 包括创建公告、发布、下载等完整流程
     */
    @Test
    public void testCompleteAttachmentDownloadFlow() throws Exception {
        log.info("开始测试完整的附件下载流程");

        // 1. 创建公告DTO
        AnnouncementDto dto = createTestAnnouncementDto("完整流程测试公告");

        // 2. 创建公告
        String announcementId = announcementApplicationService.createAnnouncement(dto);
        log.info("创建公告成功，ID：{}", announcementId);

        // 3. 验证公告创建成功
        Announcement announcement = announcementRepository.selectById(announcementId, Announcement.identifier);
        assert announcement != null : "公告应该创建成功";

        // 4. 发布公告
        announcementStatusService.publishAnnouncement(announcementId);
        log.info("发布公告成功");

        // 5. 验证公告已发布
        announcement = announcementRepository.selectById(announcementId, Announcement.identifier);
        assert "1".equals(announcement.getStatus().getValue()) : "公告应该已发布";

        // 6. 获取公告内容和附件
        AnnouncementContent content = announcementContentRepository.findByAnnouncementId(announcementId);
        assert content != null : "公告内容不应该为空";
        assert content.getAttachmentList() != null && !content.getAttachmentList().isEmpty() : "应该有附件";

        String attachmentId = content.getAttachmentList().get(0).getBid();
        log.info("获取附件ID：{}", attachmentId);

        // 7. 测试下载附件
        mockMvc.perform(get("/api/msg/announcement/v1/published/detail/attachment")
                .param("announcementId", announcementId)
                .param("attachmentId", attachmentId)
                .header("domain", "test.domain.com")
                .header("Accept", "*/*"))
                .andExpect(status().isOk());

        log.info("完整的附件下载流程测试通过");
    }

    /**
     * 测试并发下载场景
     */
    @Test
    public void testConcurrentDownload() throws Exception {
        log.info("开始测试并发下载场景");

        // 1. 创建并发布测试公告
        Announcement announcement = createAndPublishTestAnnouncement("并发下载测试公告");
        String announcementId = announcement.getBid();

        // 2. 获取附件ID
        AnnouncementContent content = announcementContentRepository.findByAnnouncementId(announcementId);
        String attachmentId = content.getAttachmentList().get(0).getBid();

        // 3. 模拟多次并发下载请求
        for (int i = 0; i < 5; i++) {
            mockMvc.perform(get("/api/msg/announcement/v1/published/detail/attachment")
                    .param("announcementId", announcementId)
                    .param("attachmentId", attachmentId)
                    .header("domain", "test.domain.com"))
                    .andExpect(status().isOk());
        }

        log.info("并发下载场景测试通过");
    }

    /**
     * 创建并发布测试公告
     */
    private Announcement createAndPublishTestAnnouncement(String name) {
        // 创建公告DTO
        AnnouncementDto dto = createTestAnnouncementDto(name);

        // 使用Service创建公告
        String announcementId = announcementApplicationService.createAnnouncement(dto);

        // 发布公告
        announcementStatusService.publishAnnouncement(announcementId);

        return announcementRepository.selectById(announcementId, Announcement.identifier);
    }

    /**
     * 创建测试用的公告DTO
     */
    private AnnouncementDto createTestAnnouncementDto(String name) {
        AnnouncementDto dto = new AnnouncementDto();
        dto.setName(name);
        dto.setType("4001");
        dto.setContent("这是测试公告的详细内容，用于验证已发布公告的查询功能。");
        dto.setReceiveType(ReceiveType.ALL_PERSONNEL);
        dto.setIsTop(false);
        dto.setDomain(Arrays.asList("test.domain.com"));
        dto.setReleaseTime(System.currentTimeMillis());
        dto.setEffectiveTime(System.currentTimeMillis() - 1000);
        dto.setExpiryTime(System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000L); // 7天后过期

        // 设置附件
        com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment attachment = new com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment();
        attachment.setNames(Lists.list("test.pdf"));
        attachment.setUrls(Lists.list("http://test.com/test.pdf"));
        dto.setAttachment(attachment);

        return dto;
    }
}
