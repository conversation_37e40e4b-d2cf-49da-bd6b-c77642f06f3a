# 已发布公告查询功能说明

## 功能概述
为公告系统新增了已发布公告的分页查询和详情查询功能，包括查看记录管理，用户可以查看已发布的公告并记录查看状态。

## 实现的功能

### 1. 已发布公告分页查询
- **功能描述**: 分页查询所有已发布状态的公告
- **查询条件**: 
  - 公告名称（模糊查询）
  - 公告类型
  - 是否置顶
  - 发布时间范围
- **返回信息**: 
  - 公告ID
  - 公告名称
  - 公告类型
  - 公告内容摘要（前100字符）
  - 发布时间
  - 是否置顶
  - 是否已查看
- **API接口**: `POST /api/msg/announcement/v1/published/page`

### 2. 已发布公告详情查询
- **功能描述**: 根据公告ID查询已发布公告的详细信息
- **返回信息**:
  - 公告ID
  - 公告名称
  - 公告类型
  - 公告完整内容
  - 发布时间
  - 生效时间
  - 到期时间
  - 是否置顶
  - 附件信息
  - 是否已查看
- **副作用**: 调用此接口会自动记录用户的查看记录
- **API接口**: `GET /api/msg/announcement/v1/published/detail`

### 3. 查看记录管理
- **自动记录**: 用户查看公告详情时自动记录查看状态
- **状态标记**: 在分页和详情查询中显示是否已查看
- **重复查看**: 重复查看会更新查看时间，不会重复创建记录

## 文件结构

### 1. 实体层
- **AnnouncementViewRecord.java**: 公告查看记录实体（已存在）

### 2. Repository层
- **AnnouncementViewRecordRepository.java**: 查看记录Repository接口
- **AnnouncementViewRecordRepositoryImpl.java**: 查看记录Repository实现
- **AnnouncementRepository.java**: 扩展了已发布公告查询方法
- **AnnouncementRepositoryImpl.java**: 实现了已发布公告查询方法

### 3. DTO和VO层
- **PublishedAnnouncementPageDto.java**: 已发布公告分页查询DTO
- **PublishedAnnouncementPageVo.java**: 已发布公告分页查询VO
- **PublishedAnnouncementDetailVo.java**: 已发布公告详情VO

### 4. 服务层
- **PublishedAnnouncementService.java**: 已发布公告服务

### 5. 控制器层
- **AnnouncementController.java**: 扩展了已发布公告查询接口

### 6. 测试层
- **PublishedAnnouncementTest.java**: 已发布公告功能测试类

## API接口详情

### 1. 分页查询已发布公告

#### 请求
```http
POST /api/msg/announcement/v1/published/page
Content-Type: application/json

{
    "pageNo": 1,
    "pageSize": 10,
    "name": "公告名称",
    "type": "公告类型",
    "isTop": true,
    "startTime": 1640995200000,
    "endTime": 1641081600000,
    "userId": "1001"
}
```

#### 响应
```json
{
    "success": true,
    "data": {
        "items": [
            {
                "bid": "announcement-id-1",
                "name": "重要通知",
                "type": "通知",
                "content": "这是公告内容摘要...",
                "publishTime": 1640995200000,
                "isTop": true,
                "isViewed": false
            }
        ],
        "pageNo": 1,
        "pageSize": 10,
        "total": 1
    }
}
```

### 2. 查询已发布公告详情

#### 请求
```http
GET /api/msg/announcement/v1/published/detail?announcementId=announcement-id-1
```

#### 响应
```json
{
    "success": true,
    "data": {
        "bid": "announcement-id-1",
        "name": "重要通知",
        "type": "通知",
        "content": "这是公告的完整内容...",
        "publishTime": 1640995200000,
        "effectiveTime": 1640995200000,
        "expiryTime": 1641600000000,
        "isTop": true,
        "attachment": {
            "fileName": "附件.pdf",
            "fileUrl": "http://example.com/file.pdf"
        },
        "isViewed": true
    }
}
```

## 业务逻辑

### 1. 分页查询逻辑
1. **状态过滤**: 只查询状态为"已发布"的公告
2. **条件过滤**: 支持按名称、类型、置顶状态、发布时间范围过滤
3. **排序规则**: 置顶公告优先，然后按发布时间倒序
4. **查看状态**: 根据用户ID查询查看记录，标记是否已查看
5. **内容摘要**: 截取公告内容前100字符作为摘要

### 2. 详情查询逻辑
1. **权限检查**: 只能查询已发布状态的公告
2. **内容查询**: 查询公告基本信息和完整内容
3. **记录查看**: 自动记录或更新用户的查看记录
4. **状态标记**: 标记为已查看状态

### 3. 查看记录逻辑
1. **首次查看**: 创建新的查看记录
2. **重复查看**: 更新现有记录的查看时间
3. **异常处理**: 查看记录失败不影响主流程
4. **状态查询**: 根据记录存在性判断是否已查看

## 数据库设计

### AnnouncementViewRecord表结构
| 字段名 | 类型 | 说明 |
|--------|------|------|
| bid | VARCHAR | 记录ID |
| announcementId | VARCHAR | 公告ID |
| userId | VARCHAR | 用户ID |
| createTime | BIGINT | 创建时间 |
| updateTime | BIGINT | 更新时间 |
| createBy | VARCHAR | 创建人 |
| updateBy | VARCHAR | 更新人 |
| tenantId | VARCHAR | 租户ID |

### 索引建议
- `idx_announcement_user` (announcementId, userId) - 用于快速查询用户对特定公告的查看记录
- `idx_user_time` (userId, updateTime) - 用于查询用户的查看历史

## 性能考虑

### 1. 查询优化
- 分页查询使用索引优化
- 查看记录查询使用复合索引
- 内容摘要在应用层处理，避免数据库函数

### 2. 缓存策略
- 可考虑对热门公告进行缓存
- 查看记录可使用Redis缓存提高性能

### 3. 异步处理
- 查看记录的创建/更新可考虑异步处理
- 避免影响主查询流程的性能

## 错误处理

### 1. 参数验证
- 公告ID不能为空
- 分页参数合法性检查
- 用户ID自动获取或验证

### 2. 业务异常
- 公告不存在
- 公告未发布
- 权限不足

### 3. 系统异常
- 数据库连接异常
- 查看记录创建失败（不影响主流程）

## 测试覆盖

### 1. 单元测试
- ✅ **PublishedAnnouncementTest.java**: 已发布公告功能测试
  - 分页查询测试
  - 详情查询测试
  - 查看记录测试
  - 查询条件过滤测试

### 2. 集成测试
- API接口测试
- 数据库操作测试
- 异常场景测试

## 使用示例

### 1. 前端分页查询
```javascript
// 查询已发布公告列表
const queryPublishedAnnouncements = async (params) => {
    const response = await fetch('/api/msg/announcement/v1/published/page', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            pageNo: params.pageNo || 1,
            pageSize: params.pageSize || 10,
            name: params.name,
            type: params.type,
            isTop: params.isTop,
            startTime: params.startTime,
            endTime: params.endTime
        })
    });
    return response.json();
};
```

### 2. 前端详情查询
```javascript
// 查询公告详情
const getAnnouncementDetail = async (announcementId) => {
    const response = await fetch(`/api/msg/announcement/v1/published/detail?announcementId=${announcementId}`);
    return response.json();
};
```

## 扩展建议

### 1. 功能扩展
- 添加公告收藏功能
- 添加公告分享功能
- 添加公告评论功能
- 添加公告阅读统计

### 2. 性能优化
- 引入搜索引擎（如Elasticsearch）
- 添加Redis缓存
- 实现CDN加速

### 3. 用户体验
- 添加公告推送通知
- 实现离线阅读
- 添加阅读进度记录

## 总结

本次实现成功为公告系统添加了完整的已发布公告查询功能，包括：

1. **完整的查询功能**: 分页查询和详情查询
2. **智能的查看记录**: 自动记录用户查看状态
3. **灵活的查询条件**: 支持多种过滤条件
4. **良好的性能设计**: 考虑了查询优化和异常处理
5. **完善的测试覆盖**: 提供了全面的单元测试

所有功能都已实现并测试通过，可以投入使用。
