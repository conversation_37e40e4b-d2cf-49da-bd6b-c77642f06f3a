package com.caidaocloud.message.service.application.announcement.dto;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.message.service.domain.announcement.enums.AnnouncementStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2025/7/11
 */
@Data
@ApiModel(description = "已发布公告分页Dto")
public class AnnouncementActivePageDto extends BasePage {
	@ApiModelProperty(value = "公告类型")
	private String type;

	@ApiModelProperty(value = "生效时间开始时间")
	private Long startTime;

	@ApiModelProperty(value = "生效时间结束时间")
	private Long endTime;

	@ApiModelProperty(value = "内容")
	private String content;
}
