package com.caidaocloud.message.service.interfaces.vo.announcement;

import java.util.List;

import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.message.service.domain.announcement.entity.AnnouncementAttachment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 已发布公告详情VO
 *
 * <AUTHOR>
 * @date 2025/7/11
 */
@Data
@ApiModel(description = "已发布公告详情VO")
public class PublishedAnnouncementDetailVo {

    @ApiModelProperty(value = "公告ID")
    private String bid;

    @ApiModelProperty(value = "公告名称")
    private String name;

    @ApiModelProperty(value = "公告类型")
    private DictSimple type;

    @ApiModelProperty(value = "公告内容")
    private String content;

    @ApiModelProperty(value = "发布时间（Unix时间戳，毫秒）")
    private Long releaseTime;

    @ApiModelProperty(value = "生效时间（Unix时间戳，毫秒）")
    private Long effectiveTime;

    @ApiModelProperty(value = "到期时间（Unix时间戳，毫秒）")
    private Long expiryTime;

    @ApiModelProperty(value = "是否置顶")
    private Boolean isTop;

    @ApiModelProperty(value = "附件")
    @Deprecated
    private Attachment attachment;

    @ApiModelProperty(value = "附件2")
    private List<AttachmentVo> attachment2;

    @Data
    public static class AttachmentVo {
        private String bid;
        private String name;
    }
}
