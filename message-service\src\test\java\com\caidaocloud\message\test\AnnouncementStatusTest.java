package com.caidaocloud.message.test;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.message.service.MessageApplication;
import com.caidaocloud.message.service.domain.announcement.entity.Announcement;
import com.caidaocloud.message.service.domain.announcement.entity.AnnouncementActive;
import com.caidaocloud.message.service.domain.announcement.enums.AnnouncementStatus;
import com.caidaocloud.message.service.domain.announcement.enums.ReceiveType;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementActiveRepository;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementRepository;
import com.caidaocloud.message.service.application.announcement.service.AnnouncementStatusService;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * 公告状态管理测试类
 *
 * <AUTHOR> Zhou
 * @date 2025/7/11
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = MessageApplication.class)
public class AnnouncementStatusTest {

    @Resource
    private AnnouncementStatusService announcementStatusService;

    @Resource
    private AnnouncementRepository announcementRepository;

    @Resource
    private AnnouncementActiveRepository announcementActiveRepository;

    @Before
    public void setUp() {
        // 设置测试用户信息
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId("11");
        userInfo.setUserId(1923451746924549L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);
    }

    /**
     * 测试发布公告功能
     */
    @Test
    public void  testPublishAnnouncement() {
        log.info("开始测试发布公告功能");

        // 1. 创建测试公告
        Announcement announcement = createTestAnnouncement();
        announcement.setEffectiveTime(System.currentTimeMillis() + 60000); // 1分钟后生效
        announcement = announcementRepository.insert(announcement);
        String announcementId = announcement.getBid();

        // 2. 发布公告
        boolean result = announcementStatusService.publishAnnouncement(announcementId);
        Assert.assertTrue("发布公告应该成功", result);

        // 3. 验证公告状态
        Announcement publishedAnnouncement = announcementRepository.selectById(announcementId, Announcement.identifier);
        Assert.assertNotNull("发布后的公告不应该为空", publishedAnnouncement);
        Assert.assertEquals("公告状态应该为已发布", AnnouncementStatus.PUBLISHED.getValue(),
                Integer.parseInt(publishedAnnouncement.getStatus().getValue()));
        Assert.assertNotNull("发布时间不应该为空", publishedAnnouncement.getReleaseTime());

        // 4. 验证未生效时不会复制到生效表
        AnnouncementActive activeAnnouncement = announcementActiveRepository.selectById(announcementId);
        Assert.assertNull("未生效的公告不应该在生效表中", activeAnnouncement);

        log.info("发布公告功能测试通过");
    }

    /**
     * 测试发布并立即生效的公告
     */
    @Test
    public void testPublishAndEffectiveAnnouncement() {
        log.info("开始测试发布并立即生效的公告");

        // 1. 创建测试公告（生效时间为当前时间）
        Announcement announcement = createTestAnnouncement();
        announcement.setEffectiveTime(System.currentTimeMillis() - 1000); // 1秒前就应该生效
        announcement = announcementRepository.insert(announcement);
        String announcementId = announcement.getBid();

        // 2. 发布公告
        boolean result = announcementStatusService.publishAnnouncement(announcementId);
        Assert.assertTrue("发布公告应该成功", result);

        // 3. 验证公告状态
        Announcement publishedAnnouncement = announcementRepository.selectById(announcementId, Announcement.identifier);
        Assert.assertEquals("公告状态应该为已发布", AnnouncementStatus.PUBLISHED.getValue(),
                Integer.parseInt(publishedAnnouncement.getStatus().getValue()));

        // 4. 验证已复制到生效表
        AnnouncementActive activeAnnouncement = announcementActiveRepository.selectById(announcementId);
        Assert.assertNotNull("已生效的公告应该在生效表中", activeAnnouncement);
        Assert.assertEquals("生效公告的原ID应该一致", announcementId, activeAnnouncement.getBid());

        log.info("发布并立即生效的公告测试通过");
    }

    /**
     * 测试撤回公告功能
     */
    @Test
    public void testWithdrawAnnouncement() {
        log.info("开始测试撤回公告功能");

        // 1. 创建并发布测试公告（未生效）
        Announcement announcement = createTestAnnouncement();
        announcement.setEffectiveTime(System.currentTimeMillis() + 60000); // 1分钟后生效
        announcement = announcementRepository.insert(announcement);
        String announcementId = announcement.getBid();

        // 发布公告
        announcementStatusService.publishAnnouncement(announcementId);

        // 2. 撤回公告
        boolean result = announcementStatusService.withdrawAnnouncement(announcementId);
        Assert.assertTrue("撤回公告应该成功", result);

        // 3. 验证公告状态
        Announcement withdrawnAnnouncement = announcementRepository.selectById(announcementId, Announcement.identifier);
        Assert.assertEquals("公告状态应该为未发布", AnnouncementStatus.UNPUBLISHED.getValue(),
                Integer.parseInt(withdrawnAnnouncement.getStatus().getValue()));
        Assert.assertNull("发布时间应该被清除", withdrawnAnnouncement.getReleaseTime());

        log.info("撤回公告功能测试通过");
    }

    /**
     * 测试失效公告功能
     */
    @Test
    public void testExpireAnnouncement() {
        log.info("开始测试失效公告功能");

        // 1. 创建并发布测试公告（已生效）
        Announcement announcement = createTestAnnouncement();
        announcement.setEffectiveTime(System.currentTimeMillis() - 1000); // 1秒前就应该生效
        announcement = announcementRepository.insert(announcement);
        String announcementId = announcement.getBid();

        // 发布公告（会自动复制到生效表）
        announcementStatusService.publishAnnouncement(announcementId);

        // 验证已复制到生效表
        AnnouncementActive activeAnnouncement = announcementActiveRepository.selectById(announcementId);
        Assert.assertNotNull("公告应该在生效表中", activeAnnouncement);

        // 2. 失效公告
        boolean result = announcementStatusService.expireAnnouncement(announcementId);
        Assert.assertTrue("失效公告应该成功", result);

        // 3. 验证公告状态
        Announcement expiredAnnouncement = announcementRepository.selectById(announcementId, Announcement.identifier);
        Assert.assertEquals("公告状态应该为失效", AnnouncementStatus.EXPIRED.getValue(),
                Integer.parseInt(expiredAnnouncement.getStatus().getValue()));

        // 4. 验证生效表中的数据已删除
        AnnouncementActive deletedActiveAnnouncement = announcementActiveRepository.selectById(announcementId);
        Assert.assertNull("生效表中的数据应该被删除", deletedActiveAnnouncement);

        log.info("失效公告功能测试通过");
    }

    /**
     * 测试编辑权限检查
     */
    @Test
    public void testCanEdit() {
        log.info("开始测试编辑权限检查");

        // 1. 创建未发布的公告
        Announcement unpublishedAnnouncement = createTestAnnouncement();
        unpublishedAnnouncement = announcementRepository.insert(unpublishedAnnouncement);
        String unpublishedId = unpublishedAnnouncement.getBid();

        // 2. 创建已发布的公告
        Announcement publishedAnnouncement = createTestAnnouncement();
        publishedAnnouncement = announcementRepository.insert(publishedAnnouncement);
        String publishedId = publishedAnnouncement.getBid();
        announcementStatusService.publishAnnouncement(publishedId);

        // 3. 验证编辑权限
        Assert.assertTrue("未发布的公告应该可以编辑", !unpublishedAnnouncement.isPublished());
        Assert.assertFalse("已发布的公告不应该可以编辑", publishedAnnouncement.isPublished());

        log.info("编辑权限检查测试通过");
    }

    /**
     * 测试删除权限检查
     */
    @Test
    public void testCanDelete() {
        log.info("开始测试删除权限检查");

        // 1. 创建未发布的公告
        Announcement unpublishedAnnouncement = createTestAnnouncement();
        unpublishedAnnouncement = announcementRepository.insert(unpublishedAnnouncement);
        String unpublishedId = unpublishedAnnouncement.getBid();

        // 2. 创建已发布的公告
        Announcement publishedAnnouncement = createTestAnnouncement();
        publishedAnnouncement = announcementRepository.insert(publishedAnnouncement);
        String publishedId = publishedAnnouncement.getBid();
        announcementStatusService.publishAnnouncement(publishedId);

        // 3. 验证删除权限
        Assert.assertTrue("未发布的公告应该可以删除", !unpublishedAnnouncement.isPublished());
        Assert.assertFalse("已发布的公告不应该可以删除",publishedAnnouncement.isPublished());

        log.info("删除权限检查测试通过");
    }

    /**
     * 创建测试用的公告实体
     */
    private Announcement createTestAnnouncement() {
        Announcement announcement = new Announcement();
        announcement.setName("测试公告");

        DictSimple type = new DictSimple();
        type.setValue("test-type");
        announcement.setType(type);

        announcement.setReleaseTime(System.currentTimeMillis());
        announcement.setEffectiveTime(System.currentTimeMillis());
        announcement.setExpiryTime(System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000L);

        announcement.setReceiveType(ReceiveType.ALL_PERSONNEL.toEnumSimple());
        announcement.setIsTop(false);
        announcement.setStatus(AnnouncementStatus.UNPUBLISHED.toEnumSimple());
        announcement.setDomain(Arrays.asList("test.domain.com"));

        return announcement;
    }
}
