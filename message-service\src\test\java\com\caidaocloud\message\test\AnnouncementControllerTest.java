package com.caidaocloud.message.test;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.message.service.MessageApplication;
import com.caidaocloud.message.service.application.announcement.dto.AnnouncementDto;
import com.caidaocloud.message.service.application.announcement.dto.AnnouncementPageDto;
import com.caidaocloud.message.service.application.announcement.service.AnnouncementApplicationService;
import com.caidaocloud.message.service.domain.announcement.entity.Announcement;
import com.caidaocloud.message.service.domain.announcement.entity.AnnouncementContent;
import com.caidaocloud.message.service.domain.announcement.entity.AnnouncementReceive;
import com.caidaocloud.message.service.domain.announcement.enums.AnnouncementStatus;
import com.caidaocloud.message.service.domain.announcement.enums.ReceiveType;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementContentRepository;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementReceiveRepository;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementRepository;
import com.caidaocloud.message.service.interfaces.facade.AnnouncementController;
import com.caidaocloud.message.service.interfaces.vo.announcement.AnnouncementPageVo;
import com.caidaocloud.message.service.interfaces.vo.announcement.AnnouncementVo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 公告Controller单元测试类
 *
 * <AUTHOR> Zhou
 * @date 2025/7/11
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = MessageApplication.class)
public class AnnouncementControllerTest {

    @Resource
    private AnnouncementController announcementController;

    @Resource
    private AnnouncementApplicationService announcementApplicationService;

    @Resource
    private AnnouncementRepository announcementRepository;

    @Resource
    private AnnouncementContentRepository announcementContentRepository;

    @Resource
    private AnnouncementReceiveRepository announcementReceiveRepository;

    private String testAnnouncementId;

    @Before
    public void setUp() {
        // 设置测试用户信息
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId("11");
        userInfo.setUserId(1923451746924549L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);
    }

    /**
     * 测试创建公告
     */
    @Test
    public void testCreateAnnouncement() {
        log.info("开始测试创建公告");

        // 准备测试数据
        AnnouncementDto dto = createTestAnnouncementDto();

        // 调用接口
        Result<String> result = announcementController.createAnnouncement(dto);

        // 验证结果
        Assert.assertTrue("创建公告应该成功", result.isSuccess());
        Assert.assertNotNull("公告ID不应该为空", result.getData());
        Assert.assertTrue("公告ID应该不为空字符串", StringUtils.hasText(result.getData()));

        testAnnouncementId = result.getData();
        log.info("创建公告成功，ID：{}", testAnnouncementId);

        // 验证数据库中的数据
        verifyAnnouncementInDatabase(testAnnouncementId, dto);
    }

    /**
     * 测试更新公告
     */
    @Test
    public void testUpdateAnnouncement() {
        log.info("开始测试更新公告");

        // 先创建一个公告
        // AnnouncementDto createDto = createTestAnnouncementDto();
        // Result<String> createResult = announcementController.createAnnouncement(createDto);
        // Assert.assertTrue("创建公告应该成功", createResult.isSuccess());
        // String announcementId = createResult.getData();

        // 准备更新数据
        // AnnouncementDto updateDto = createTestAnnouncementDto();
        AnnouncementDto updateDto = FastjsonUtil.convertObject("{\n"
                + "    \"name\": \"测试公告标题-董\",\n"
                + "    \"type\": \"4001\",\n"
                + "    \"receiveType\": \"DESIGNATED_PERSONNEL\",\n"
                + "    \"receiveEmpList\": [\n"
                + "        {\n"
                + "            \"name\": \"董薇1028w\",\n"
                + "            \"workno\": \"10281\",\n"
                + "            \"enName\": null,\n"
                + "            \"empId\": \"2047437853497344\",\n"
                + "            \"isHidden\": null,\n"
                + "            \"leaveDate\": 1732636800000,\n"
                + "            \"post\": \"1990073313146880\",\n"
                + "            \"empType\": {\n"
                + "                \"text\": null,\n"
                + "                \"value\": \"\",\n"
                + "                \"code\": null\n"
                + "            },\n"
                + "            \"organize\": \"1990073027450880\"\n"
                + "        }\n"
                + "    ],\n"
                + "    \"isTop\": false,\n"
                + "    \"content\": \"<p>这是一个测试公告的内容</p>\",\n"
                + "    \"domain\": [\n"
                + "        \"test.domain.com\"\n"
                + "    ],\n"
                + "    \"bid\": \"2229382739638272\",\n"
                + "    \"effectiveTime\": 1752307913100,\n"
                + "    \"expiryTime\": 1752912713100,\n"
                + "    \"attachment\": {\n"
                + "        \"names\": [\n"
                + "            \"test.pdf\"\n"
                + "        ],\n"
                + "        \"urls\": [\n"
                + "            \"http://test.com/test.pdf\"\n"
                + "        ]\n"
                + "    }\n"
                + "}", AnnouncementDto.class);
   String     announcementId = "2229382739638272";

        // 调用更新接口
        Result<Boolean> updateResult = announcementController.updateAnnouncement(updateDto);

        // 验证结果
        Assert.assertTrue("更新公告应该成功", updateResult.isSuccess());
        Assert.assertTrue("更新结果应该为true", updateResult.getData());

        log.info("更新公告成功，ID：{}", announcementId);

        // 验证更新后的数据
        verifyAnnouncementInDatabase(announcementId, updateDto);

        AnnouncementVo vo = announcementController.getAnnouncementById(announcementId).getData();
        Assert.assertEquals(updateDto.getReceiveEmpList(), vo.getReceiveEmpList());

    }

    /**
     * 测试查询公告详情
     */
    @Test
    public void testGetAnnouncementById() {
        log.info("开始测试查询公告详情");

        // 先创建一个公告
        AnnouncementDto createDto = createTestAnnouncementDto();
        Result<String> createResult = announcementController.createAnnouncement(createDto);
        Assert.assertTrue("创建公告应该成功", createResult.isSuccess());
        String announcementId = createResult.getData();

        // 调用查询接口
        Result<AnnouncementVo> queryResult = announcementController.getAnnouncementById(announcementId);

        // 验证结果
        Assert.assertTrue("查询公告应该成功", queryResult.isSuccess());
        Assert.assertNotNull("公告详情不应该为空", queryResult.getData());

        AnnouncementVo vo = queryResult.getData();
        Assert.assertEquals("公告ID应该一致", announcementId, vo.getBid());
        Assert.assertEquals("公告名称应该一致", createDto.getName(), vo.getName());
        Assert.assertEquals("公告类型应该一致", createDto.getType(), vo.getType());
        Assert.assertEquals("公告内容应该一致", createDto.getContent(), vo.getContent());

        log.info("查询公告详情成功：{}", FastjsonUtil.toJson(vo));
    }

    /**
     * 测试分页查询公告
     */
    @Test
    public void testPageAnnouncements() {
        log.info("开始测试分页查询公告");

        // 先创建几个公告
        for (int i = 1; i <= 3; i++) {
            AnnouncementDto dto = createTestAnnouncementDto();
            dto.setName("测试公告" + i);
            dto.setType("4001");
            announcementController.createAnnouncement(dto);
        }

        // 准备分页查询参数
        AnnouncementPageDto pageDto = new AnnouncementPageDto();
        pageDto.setPageNo(1);
        pageDto.setPageSize(10);
        pageDto.setName("测试公告");

        // 调用分页查询接口
        Result<PageResult<AnnouncementPageVo>> pageResult = announcementController.page(pageDto);

        // 验证结果
        Assert.assertTrue("分页查询应该成功", pageResult.isSuccess());
        Assert.assertNotNull("分页结果不应该为空", pageResult.getData());
        Assert.assertTrue("应该有查询结果", pageResult.getData().getTotal() > 0);

        log.info("分页查询成功，总数：{}", pageResult.getData().getTotal());
    }

    /**
     * 测试删除公告
     */
    @Test
    public void testDeleteAnnouncement() {
        log.info("开始测试删除公告");

        // 先创建一个公告
        AnnouncementDto createDto = createTestAnnouncementDto();
        Result<String> createResult = announcementController.createAnnouncement(createDto);
        Assert.assertTrue("创建公告应该成功", createResult.isSuccess());
        String announcementId = createResult.getData();

        // 调用删除接口
        Result<Boolean> deleteResult = announcementController.deleteAnnouncement(announcementId);

        // 验证结果
        Assert.assertTrue("删除公告应该成功", deleteResult.isSuccess());
        Assert.assertTrue("删除结果应该为true", deleteResult.getData());

        log.info("删除公告成功，ID：{}", announcementId);

        // 验证公告已被删除
        Result<AnnouncementVo> queryResult = announcementController.getAnnouncementById(announcementId);
        Assert.assertFalse("查询已删除的公告应该失败", queryResult.isSuccess());
    }

    /**
     * 创建测试用的公告DTO
     */
    private AnnouncementDto createTestAnnouncementDto() {
        AnnouncementDto dto = new AnnouncementDto();
        dto.setName("测试公告标题");
        dto.setType("4001");
        dto.setContent("这是一个测试公告的内容");
        dto.setReceiveType(ReceiveType.ALL_PERSONNEL);
        dto.setReceiveDeptList(Arrays.asList("all"));
        dto.setIsTop(false);
        dto.setDomain(Arrays.asList("test.domain.com"));
        dto.setReleaseTime(System.currentTimeMillis());
        dto.setEffectiveTime(System.currentTimeMillis());
        dto.setExpiryTime(System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000L); // 7天后过期

        // 设置附件
        Attachment attachment = new Attachment();
        attachment.setNames(Lists.list("test.pdf"));
        attachment.setUrls(Lists.list("http://test.com/test.pdf"));
        dto.setAttachment(attachment);

        return dto;
    }

    /**
     * 验证数据库中的公告数据
     */
    private void verifyAnnouncementInDatabase(String announcementId, AnnouncementDto expectedDto) {
        log.info("验证数据库中的公告数据，ID：{}", announcementId);

        // 验证公告基本信息
        Announcement announcement = announcementRepository.selectById(announcementId, Announcement.identifier);
        Assert.assertNotNull("公告基本信息应该存在", announcement);
        Assert.assertEquals("公告名称应该一致", expectedDto.getName(), announcement.getName());
        Assert.assertEquals("公告类型应该一致", expectedDto.getType(), announcement.getType().getValue());
        Assert.assertEquals("默认状态应该是未发布", AnnouncementStatus.UNPUBLISHED.getValue(), 
                           Integer.parseInt(announcement.getStatus().getValue()));

        // 验证公告内容
        if (StringUtils.hasText(expectedDto.getContent()) || expectedDto.getAttachment() != null) {
            AnnouncementContent content = announcementContentRepository.findByAnnouncementId(announcementId);
            Assert.assertNotNull("公告内容应该存在", content);
            Assert.assertEquals("公告内容应该一致", expectedDto.getContent(), content.getContent());
            if (expectedDto.getAttachment() != null) {
                Assert.assertNotNull("附件信息应该存在", content.getAttachment());
            }
        }

        // 验证公告接收者信息
        if (expectedDto.getReceiveDeptList() != null && !expectedDto.getReceiveDeptList().isEmpty()) {
            List<AnnouncementReceive> receiveList = announcementReceiveRepository.findByAnnouncementId(announcementId);
            Assert.assertNotNull("接收者信息应该存在", receiveList);
            Assert.assertEquals("接收者数量应该一致", expectedDto.getReceiveDeptList().size(), receiveList.size());
        }

        log.info("数据库验证通过");
    }

    @Test
    public void empSelect(){

            AnnouncementDto dto = new AnnouncementDto();
            dto.setName("测试公告标题");
            dto.setType("4001");
            dto.setContent("这是一个测试公告的内容");
            dto.setReceiveType(ReceiveType.DESIGNATED_PERSONNEL);
        EmpSimple empSimple = new EmpSimple();
        empSimple.setEmpId("1");

        EmpSimple empSimple1 = new EmpSimple();
        empSimple1.setEmpId("2");
        dto.setReceiveEmpList(Arrays.asList(empSimple, empSimple1));
            dto.setIsTop(false);
            dto.setDomain(Arrays.asList("test.domain.com"));
            dto.setReleaseTime(System.currentTimeMillis());
            dto.setEffectiveTime(System.currentTimeMillis());
            dto.setExpiryTime(System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000L); // 7天后过期

            // 设置附件
            Attachment attachment = new Attachment();
            attachment.setNames(Lists.list("test.pdf"));
            attachment.setUrls(Lists.list("http://test.com/test.pdf"));
            dto.setAttachment(attachment);

        System.out.println(announcementController.createAnnouncement(dto).getData());
    }
}
