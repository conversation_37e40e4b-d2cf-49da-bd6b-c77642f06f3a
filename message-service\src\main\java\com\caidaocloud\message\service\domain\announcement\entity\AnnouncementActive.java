package com.caidaocloud.message.service.domain.announcement.entity;

import java.util.List;

import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsArray;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

@Data
public class AnnouncementActive extends DataSimple {

    private String name;

    private DictSimple type;

    private EnumSimple receiveType;

    private Long releaseTime;

    private Long effectiveTime;

    private Long expiryTime;

    private Boolean isTop = false;

    @DisplayAsArray
    private List<String> domain;

    public static String identifier = "entity.message.AnnouncementActive";

    public AnnouncementActive() {
        setIdentifier(identifier);
        setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId());
        setCreateTime(System.currentTimeMillis());
        setCreateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
        setUpdateTime(System.currentTimeMillis());
        setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
    }

    public boolean checkDomain(String currentDomain) {
        if (CollectionUtils.isEmpty(domain)) {
            return true;
        }
        return domain.contains(currentDomain);
    }
}