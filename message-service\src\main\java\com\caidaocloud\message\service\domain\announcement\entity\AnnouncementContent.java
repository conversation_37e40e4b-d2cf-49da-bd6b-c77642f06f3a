package com.caidaocloud.message.service.domain.announcement.entity;

import java.util.ArrayList;
import java.util.List;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.Data;

@Data
public class AnnouncementContent extends DataSimple {

    private String announcementId;

    private String content;

    @Deprecated
    private Attachment attachment;

    private List<AnnouncementAttachment> attachmentList = new ArrayList<>();

    public static String identifier = "entity.message.AnnouncementContent";

    public AnnouncementContent() {
        setIdentifier(identifier);
        setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId());
        setCreateTime(System.currentTimeMillis());
        setCreateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
        setUpdateTime(System.currentTimeMillis());
        setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
    }
}