package com.caidaocloud.message.service.infrastructure.repository.impl.announcement;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.message.service.domain.announcement.entity.AnnouncementReceive;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementReceiveRepository;
import com.caidaocloud.message.service.domain.base.repository.BaseRepositoryImpl;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 公告接收者Repository实现类
 *
 * <AUTHOR>
 * @date 2025/7/11
 */
@Repository
public class AnnouncementReceiveRepositoryImpl extends BaseRepositoryImpl<AnnouncementReceive> implements AnnouncementReceiveRepository {
    
    @Override
    public List<AnnouncementReceive> findByAnnouncementId(String announcementId) {
        DataFilter filter = getBaseFilter()
                .andEq("announcementId", announcementId);

        PageResult<AnnouncementReceive> result = DataQuery.identifier(AnnouncementReceive.identifier)
                .limit(-1, 1)
                .decrypt()
                .specifyLanguage()
                .queryInvisible()
                .filter(filter, AnnouncementReceive.class);
        
        return getPageList(result);
    }
    
    @Override
    public int deleteByAnnouncementId(String announcementId) {
        DataFilter filter = getBaseFilter()
                .andEq("announcementId", announcementId);
        DataDelete.identifier(AnnouncementReceive.identifier).batchDelete(filter);
        return 1;
    }
    
    @Override
    public void batchInsert(List<AnnouncementReceive> receiveList) {
        if (receiveList != null && !receiveList.isEmpty()) {
            for (AnnouncementReceive receive : receiveList) {
                DataInsert.identifier(AnnouncementReceive.identifier).insert(receive);
            }
        }
    }
}
