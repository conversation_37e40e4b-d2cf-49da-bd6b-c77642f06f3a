package com.caidaocloud.message.service.infrastructure.repository.impl.announcement;

import java.io.Serializable;
import java.util.List;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.message.service.domain.announcement.entity.AnnouncementAttachment;
import com.caidaocloud.message.service.domain.announcement.entity.AnnouncementContent;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementContentRepository;
import com.caidaocloud.message.service.domain.base.repository.BaseRepositoryImpl;
import springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator;

import org.springframework.stereotype.Repository;

/**
 * 公告内容Repository实现类
 *
 * <AUTHOR>
 * @date 2025/7/11
 */
@Repository
public class AnnouncementContentRepositoryImpl extends BaseRepositoryImpl<AnnouncementContent> implements AnnouncementContentRepository {


    @Override
    public AnnouncementContent findByAnnouncementId(String announcementId) {
        DataFilter filter = getBaseFilter()
                .andEq("announcementId", announcementId);
        
        PageResult<AnnouncementContent> result = DataQuery.identifier(AnnouncementContent.identifier)
                .decrypt()
                .specifyLanguage()
                .queryInvisible()
                .filter(filter, AnnouncementContent.class);
        
        if (result != null && result.getItems() != null && !result.getItems().isEmpty()) {
            AnnouncementContent content = result.getItems().get(0);
            List<AnnouncementAttachment> list = loadAttachment(content);
            content.setAttachmentList(list);
            return content;
        }
        return null;
    }
    
    @Override
    public int deleteByAnnouncementId(String announcementId) {
        DataFilter filter = getBaseFilter()
                .andEq("announcementId", announcementId);
        
        DataDelete.identifier(AnnouncementContent.identifier).batchDelete(filter);
        return 1;
    }


    @Override
    public int deleteById(Serializable id) {
        DataDelete.identifier(AnnouncementContent.identifier).softDelete(String.valueOf(id));
        return 1;
    }

    @Override
    public AnnouncementContent insert(AnnouncementContent data) {
        AnnouncementContent content = super.insert(data);
        saveAttachment(data);
        return content;
    }

    @Override
    public int updateById(AnnouncementContent data) {
        int r = super.updateById(data);
        updateAttachment(data);
        return r;
    }

    private void updateAttachment(AnnouncementContent data) {
        List<AnnouncementAttachment> list = loadAttachment(data);
        for (AnnouncementAttachment attachment : list) {
            DataDelete.identifier(AnnouncementAttachment.identifier).delete(attachment.getBid());
        }
        saveAttachment(data);
    }

    private List<AnnouncementAttachment> loadAttachment(AnnouncementContent data) {
        List<AnnouncementAttachment> list = DataQuery.identifier(AnnouncementAttachment.identifier).limit(-1, 1)
                .filter(DataFilter.eq("deleted", Boolean.FALSE.toString())
                        .andEq("announcementId", data.getAnnouncementId()), AnnouncementAttachment.class).getItems();
        return list;
    }

    private void saveAttachment(AnnouncementContent data) {
        for (AnnouncementAttachment attachment : data.getAttachmentList()) {
            attachment.setAnnouncementId(data.getAnnouncementId());
            DataInsert.identifier(AnnouncementAttachment.identifier).insert(attachment);
        }
    }

}
