package com.caidaocloud.message.service.infrastructure.repository.impl.announcement;

import java.io.Serializable;
import java.util.List;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.filter.MultiDataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.message.service.application.announcement.dto.AnnouncementPageDto;
import com.caidaocloud.message.service.domain.announcement.entity.Announcement;
import com.caidaocloud.message.service.domain.announcement.enums.AnnouncementStatus;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementRepository;
import com.caidaocloud.message.service.domain.base.repository.BaseRepositoryImpl;
import org.apache.commons.lang3.StringUtils;

import org.springframework.stereotype.Repository;

/**
 * 公告Repository实现类
 *
 * <AUTHOR>
 * @date 2025/7/11
 */
@Repository
public class AnnouncementRepositoryImpl extends BaseRepositoryImpl<Announcement> implements AnnouncementRepository {
	@Override
	public PageResult<Announcement> selectPage(AnnouncementPageDto dto) {
		DataFilter filter = getBaseFilter();
		filter = filter.andRegexIf("name", dto.getName(), () -> StringUtils.isNotEmpty(dto.getName()))
				.andEqIf("type$dictValue", dto.getType(), () -> StringUtils.isNotEmpty(dto.getType()));
		if (dto.getStatus() != null) {
			filter = filter.andEq("status", String.valueOf(dto.getStatus().getValue()));
		}
		if (dto.getStartTime() != null) {
			filter = filter.andGe("effectiveTime", String.valueOf(dto.getStartTime()));
		}
		if (dto.getEndTime() != null) {
			filter = filter.andLe("effectiveTime", String.valueOf(dto.getEndTime()));
		}
		if (!dto.getExpired()) {
			filter = filter.andNe("status", String.valueOf(AnnouncementStatus.EXPIRED.getValue()));
		}
		filter = (DataFilter) dto.doDataFilter(dto.getFilters(), filter);
		return DataQuery.identifier(Announcement.identifier).limit(dto.getPageSize(), dto.getPageNo())
				.filter(filter, Announcement.class, "id", System.currentTimeMillis());
	}

	@Override
	public int deleteById(Serializable id) {
		DataDelete.identifier(Announcement.identifier).softDelete(String.valueOf(id));
		return 1;
	}

	@Override
	public Announcement selectById(Serializable bid) {
		DataFilter filter = getBaseFilter().andEq("bid", String.valueOf(bid));
		List<Announcement> list = DataQuery.identifier(Announcement.identifier).filter(filter, Announcement.class)
				.getItems();
		return list.isEmpty() ? null : list.get(0);
	}

	@Override
	public List<Announcement> findPublishedAndEffectiveAnnouncements(Long currentTime) {
		DataFilter filter = getBaseFilter()
				.andEq("status", String.valueOf(AnnouncementStatus.PUBLISHED.getValue()))
				.andLe("effectiveTime", String.valueOf(currentTime));

		PageResult<Announcement> result = DataQuery.identifier(Announcement.identifier)
				.limit(-1,1)
				.decrypt()
				.specifyLanguage()
				.queryInvisible()
				.filter(filter, Announcement.class);

		return getPageList(result);
	}

	@Override
	public List<Announcement> findPublishedAndExpiredAnnouncements(Long currentTime) {
		DataFilter filter = getBaseFilter()
				.andEq("status", String.valueOf(AnnouncementStatus.PUBLISHED.getValue()))
				.andLe("expiryTime", String.valueOf(currentTime));

		PageResult<Announcement> result = DataQuery.identifier(Announcement.identifier)
				.limit(-1, 1)
				.decrypt()
				.specifyLanguage()
				.queryInvisible()
				.filter(filter, Announcement.class);

		return getPageList(result);
	}
}
