package com.caidaocloud.message.service.domain.announcement.repository;

import com.caidaocloud.message.service.domain.announcement.entity.AnnouncementContent;
import com.caidaocloud.message.service.domain.base.repository.BaseRepository;

/**
 * 公告内容Repository接口
 *
 * <AUTHOR>
 * @date 2025/7/11
 */
public interface AnnouncementContentRepository extends BaseRepository<AnnouncementContent> {
    
    /**
     * 根据公告ID查询公告内容
     * @param announcementId 公告ID
     * @return 公告内容
     */
    AnnouncementContent findByAnnouncementId(String announcementId);
    
    /**
     * 根据公告ID删除公告内容
     * @param announcementId 公告ID
     * @return 删除数量
     */
    int deleteByAnnouncementId(String announcementId);
    
}
