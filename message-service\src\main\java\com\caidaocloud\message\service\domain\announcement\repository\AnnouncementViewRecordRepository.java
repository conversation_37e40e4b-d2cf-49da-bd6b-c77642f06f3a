package com.caidaocloud.message.service.domain.announcement.repository;

import java.util.List;

import com.caidaocloud.message.service.domain.announcement.entity.AnnouncementViewRecord;
import com.caidaocloud.message.service.domain.base.repository.BaseRepository;

/**
 * 公告查看记录Repository接口
 *
 * <AUTHOR>
 * @date 2025/7/11
 */
public interface AnnouncementViewRecordRepository extends BaseRepository<AnnouncementViewRecord> {
    
    /**
     * 根据公告ID和用户ID查询查看记录
     * @param announcementId 公告ID
     * @param userId 用户ID
     * @return 查看记录
     */
    AnnouncementViewRecord findByAnnouncementIdAndUserId(String announcementId, String userId);
    
    /**
     * 根据公告ID和用户ID删除查看记录
     * @param announcementId 公告ID
     * @param userId 用户ID
     * @return 删除数量
     */
    int deleteByAnnouncementIdAndUserId(String announcementId, String userId);
    
    /**
     * 根据公告ID删除所有查看记录
     * @param announcementId 公告ID
     * @return 删除数量
     */
    int deleteByAnnouncementId(String announcementId);

	List<AnnouncementViewRecord> listRecord(List<String> idList);
}
