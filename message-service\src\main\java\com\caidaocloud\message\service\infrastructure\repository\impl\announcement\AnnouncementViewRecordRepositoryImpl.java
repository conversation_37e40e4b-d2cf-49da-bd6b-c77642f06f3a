package com.caidaocloud.message.service.infrastructure.repository.impl.announcement;

import java.util.List;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.message.service.domain.announcement.entity.AnnouncementViewRecord;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementViewRecordRepository;
import com.caidaocloud.message.service.domain.base.repository.BaseRepositoryImpl;
import com.caidaocloud.security.util.SecurityUserUtil;

import org.springframework.stereotype.Repository;
import org.springframework.util.function.SupplierUtils;

/**
 * 公告查看记录Repository实现类
 *
 * <AUTHOR>
 * @date 2025/7/11
 */
@Repository
public class AnnouncementViewRecordRepositoryImpl extends BaseRepositoryImpl<AnnouncementViewRecord> implements AnnouncementViewRecordRepository {
    
    @Override
    public AnnouncementViewRecord findByAnnouncementIdAndUserId(String announcementId, String userId) {
        DataFilter filter = getBaseFilter()
                .andEq("announcementId", announcementId)
                .andEq("userId", userId);
        
        PageResult<AnnouncementViewRecord> result = DataQuery.identifier(AnnouncementViewRecord.identifier)
                .decrypt()
                .specifyLanguage()
                .queryInvisible()
                .filter(filter, AnnouncementViewRecord.class);
        
        if (result != null && result.getItems() != null && !result.getItems().isEmpty()) {
            return result.getItems().get(0);
        }
        return null;
    }
    
    @Override
    public int deleteByAnnouncementIdAndUserId(String announcementId, String userId) {
        DataFilter filter = getBaseFilter()
                .andEq("announcementId", announcementId)
                .andEq("userId", userId);
        
        DataDelete.identifier(AnnouncementViewRecord.identifier).batchDelete(filter);
        return 1;
    }
    
    @Override
    public int deleteByAnnouncementId(String announcementId) {
        DataFilter filter = getBaseFilter()
                .andEq("announcementId", announcementId);
        
        DataDelete.identifier(AnnouncementViewRecord.identifier).batchDelete(filter);
        return 1;
    }

    @Override
    public List<AnnouncementViewRecord> listRecord(List<String> idList) {
        DataFilter filter = getBaseFilter()
                .andIn("announcementId", idList)
                .andEq("userId", String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));

        return DataQuery.identifier(AnnouncementViewRecord.identifier)
                .limit(-1, 1)
                .decrypt()
                .specifyLanguage()
                .queryInvisible()
                .filter(filter, AnnouncementViewRecord.class).getItems();
    }
}
