package com.caidaocloud.message.service.domain.announcement.repository;

import com.caidaocloud.message.service.domain.announcement.entity.AnnouncementReceive;
import com.caidaocloud.message.service.domain.base.repository.BaseRepository;

import java.util.List;

/**
 * 公告接收者Repository接口
 *
 * <AUTHOR>
 * @date 2025/7/11
 */
public interface AnnouncementReceiveRepository extends BaseRepository<AnnouncementReceive> {
    
    /**
     * 根据公告ID查询接收者列表
     * @param announcementId 公告ID
     * @return 接收者列表
     */
    List<AnnouncementReceive> findByAnnouncementId(String announcementId);
    
    /**
     * 根据公告ID删除接收者信息
     * @param announcementId 公告ID
     * @return 删除数量
     */
    int deleteByAnnouncementId(String announcementId);
    
    /**
     * 批量插入接收者信息
     * @param receiveList 接收者列表
     */
    void batchInsert(List<AnnouncementReceive> receiveList);
    
}
