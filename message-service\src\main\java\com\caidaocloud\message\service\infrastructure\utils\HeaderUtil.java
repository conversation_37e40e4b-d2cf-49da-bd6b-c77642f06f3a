package com.caidaocloud.message.service.infrastructure.utils;

import java.net.URL;
import java.util.Optional;

import javax.servlet.http.HttpServletRequest;

import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

public class HeaderUtil {

    /**
     * Retrieves the value of the specified header from the current HTTP request.
     *
     * @param headerName The name of the header to retrieve.
     * @return An Optional containing the header value if present, or empty if not found or request is unavailable.
     */
    public static Optional<String> getHeader(String headerName) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return Optional.empty();
        }
        HttpServletRequest request = attributes.getRequest();
        String header = request.getHeader(headerName);
        if (header == null) {
            return Optional.empty();
        }
        return Optional.ofNullable(removeProtocol(header));
    }

    public static String removeProtocol(String urlString) {
        try {
            URL url = new URL(urlString);
            String host = url.getHost();
            int port = url.getPort(); // 返回 -1 如果没有端口
            return port == -1 ? host : host + ":" + port;
        } catch (Exception e) {
            return urlString; // 解析失败返回原字符串
        }
    }

}