package com.caidaocloud.message.service.application.feign;

import com.caidaocloud.hr.service.vo.EmpPrivateInfoVo;
import com.caidaocloud.hr.service.vo.EmpWorkInfoVo;
import com.caidaocloud.message.service.interfaces.dto.base.UserDetailVo;
import com.caidaocloud.message.service.interfaces.dto.emp.EmpWorkInfoDto;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import io.swagger.annotations.ApiOperation;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 *
 * <AUTHOR>
 * @date 2023/7/27
 */
@FeignClient(
		value = "caidaocloud-masterdata-service",
		fallback = MasterdataFeignClientFallback.class,
		configuration = FeignConfiguration.class,
		contextId = "MasterdataFeignClientV2"
)
public interface MasterdataFeignClientV2 {
	@GetMapping("/api/masterdata/v2/emp/hrbp")
	Result<EmpWorkInfoDto> loadEmpHrbp(@RequestParam("empId") String empId, @RequestParam("datetime") Long datetime);

	@GetMapping("/api/masterdata/v2/emp/leader")
	Result<EmpWorkInfoDto> loadEmpLeader(@RequestParam("empId") String empId, @RequestParam("datetime") Long datetime);

	@GetMapping("/api/masterdata/v2/emp/org/leader")
	Result<EmpWorkInfoDto> loadEmpOrgLeader(@RequestParam("empId") String empId, @RequestParam("datetime") Long datetime);

	@GetMapping("/api/masterdata/v2/emp/privateInfo")
	Result<EmpPrivateInfoVo> loadPrivateInfo(@RequestParam("empId") String empId);

	@GetMapping("/api/masterdata/v2/emp/loadEmpInfoByWorkno")
	Result<EmpWorkInfoDto> loadEmpInfoByWorkno(@RequestParam(value = "workno") String workno, @RequestParam(value = "datetime") Long datetime);


	@GetMapping("/api/masterdata/v2/emp/workInfo")
	Result<EmpWorkInfoVo> loadEmpWorkInfo(@RequestParam("empId") String empId, @RequestParam(value = "datetime", required = false) Long datetime);

	@GetMapping("/api/masterData/emp/v2/detail")
	Result<UserDetailVo> loadCurrentUserDetail();
}
